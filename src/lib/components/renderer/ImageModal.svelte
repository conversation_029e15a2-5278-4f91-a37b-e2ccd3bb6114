<!-- ImageModal.svelte -->
<script lang="ts">
	import { createEventDispatcher, onMount, onDestroy } from 'svelte';
	import { fade, scale } from 'svelte/transition';
	import { t } from '$lib/stores/i18n';
	
	export let isOpen = false;
	export let imageUrl = '';
	export let imageAlt = '';
	
	const dispatch = createEventDispatcher();
	
	function closeModal() {
		console.log('Closing modal'); // Debug log
		isOpen = false;
		dispatch('close');
	}
	
	function handleKeydown(event: KeyboardEvent) {
		console.log('Key pressed:', event.key); // Debug log
		if (event.key === 'Escape') {
			event.preventDefault();
			closeModal();
		}
	}
	
	function handleBackdropClick(event: MouseEvent) {
		console.log('Backdrop clicked', event.target, event.currentTarget); // Debug log
		// Close modal when clicking on the backdrop (dark area)
		const target = event.target as HTMLElement;
		const currentTarget = event.currentTarget as HTMLElement;
		
		// Check if we clicked directly on the backdrop or any non-interactive element
		if (target === currentTarget || target.classList.contains('modal-backdrop-area')) {
			closeModal();
		}
	}
	
	// Manage body scroll when modal opens/closes
	$: if (isOpen) {
		document.body.classList.add('modal-open');
	} else {
		document.body.classList.remove('modal-open');
	}
	
	// Cleanup on component destroy
	onDestroy(() => {
		document.body.classList.remove('modal-open');
	});
</script>

<svelte:window on:keydown={handleKeydown} />

{#if isOpen}
	<!-- Modal Backdrop - Click anywhere here to close -->
	<div 
		class="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4 cursor-pointer"
		on:click={handleBackdropClick}
		on:keydown={handleKeydown}
		role="dialog"
		aria-modal="true"
		aria-label="Image viewer"
		tabindex="-1"
		transition:fade={{ duration: 200 }}
	>
		<!-- Close Button -->
		<button 
			class="absolute top-4 right-4 text-white hover:text-gray-300 transition-colors z-10 bg-black bg-opacity-50 rounded-full p-2 hover:bg-opacity-70"
			on:click|stopPropagation={closeModal}
			aria-label="Close image viewer"
		>
			<svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
				<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
			</svg>
		</button>
		
		<!-- Image Container -->
		<div class="w-full h-full flex items-center justify-center overflow-hidden cursor-pointer modal-backdrop-area" on:click={handleBackdropClick}>
			<img 
				src={imageUrl}
				alt={imageAlt}
				class="max-w-[90vw] max-h-[90vh] w-auto h-auto object-contain rounded-lg shadow-2xl cursor-default"
				on:click|stopPropagation={() => {}}
				transition:scale={{ duration: 200, start: 0.8 }}
			/>
		</div>
		
		<!-- Download Button -->
		<a 
			href={imageUrl}
			download
			class="absolute bottom-4 right-4 bg-black bg-opacity-50 hover:bg-opacity-70 text-white px-4 py-2 rounded-lg transition-all duration-200 flex items-center gap-2"
			on:click|stopPropagation
			aria-label="Download image"
		>
			<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
				<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
			</svg>
			{t('download')}
		</a>
		
		<!-- Instructions hint -->
		<div class="absolute bottom-4 left-4 text-white text-sm opacity-70 pointer-events-none">
			{t('image_modal_close_hint')}
		</div>
	</div>
{/if}

<style>
	/* Prevent body scroll when modal is open */
	:global(body.modal-open) {
		overflow: hidden;
	}
	
	/* Add some nice transitions */
	:global(.modal-backdrop) {
		backdrop-filter: blur(2px);
	}
</style>