<script lang="ts">
	import { t } from '$lib/stores/i18n';
	import { enhance } from '$app/forms';
	import { Button, Modal, Label, Input, Checkbox, Alert } from 'flowbite-svelte';
	import { EyeSolid, EyeSlashSolid } from 'flowbite-svelte-icons';
	import { handleEnhance } from '$lib/components/Enhance/SubmissionHandleEnhance.svelte';
	import { toastStore } from '$lib/stores/toastStore';

	export let user: any;

	import { page } from '$app/stores';

	$: role = $page.data.role;

	let editForm: HTMLFormElement;
	let editModalOpen = false;
	let selectInstances: any = null;

	// State variables for handling messages
	let isSubmitting = false;
	let isPasswordSubmitting = false;

	// Password modal state
	let passwordModalOpen = false;
	let passwordForm: HTMLFormElement;
	let passwordFormData = {
		new_password: '',
		confirm_password: ''
	};
	let passwordFieldsEverTyped = false;

	// Password visibility toggles
	let showNewPassword = false;
	let showConfirmPassword = false;

	// Password validation (from UserSignUp.svelte)
	const specialChars = '!@#$%^&*';

	function checkPasswordRules(password: string) {
		return {
			length: password.length > 8,
			lowercase: /[a-z]/.test(password),
			uppercase: /[A-Z]/.test(password),
			special: new RegExp(`[${specialChars.replace(/[-/\\^$*+?.()|[\]{}]/g, '\\$&')}]`).test(
				password
			),
			number: /[0-9]/.test(password)
		};
	}

	function dismissPasswordAlerts() {
		// Function to dismiss password-related alerts when user starts typing
		// This maintains consistency with UserSignUp.svelte pattern
		// Future enhancement: integrate with field-specific error handling
	}

	// Combined handler for password input (validation + alert dismissal)
	function handlePasswordInput() {
		passwordFieldsEverTyped = true;
		dismissPasswordAlerts();
	}

	// Password visibility toggle functions
	function toggleNewPasswordVisibility() {
		showNewPassword = !showNewPassword;
	}

	function toggleConfirmPasswordVisibility() {
		showConfirmPassword = !showConfirmPassword;
	}

	$: passwordRulesStatus = checkPasswordRules(passwordFormData.new_password);
	$: allPasswordRulesPassed = Object.values(passwordRulesStatus).every((value) => value === true);
	$: passwordsMatch =
		passwordFormData.new_password === passwordFormData.confirm_password &&
		passwordFormData.new_password.length > 0;

	// Handle form submission results
	const handleSubmissionResult = (result: any, isPassword: boolean = false) => {
		if (result.type === 'failure') {
			toastStore.add(result.data?.error || 'Operation failed', 'error');
		} else if (result.type === 'success') {
			toastStore.add(
				// result.data?.res_msg ||
				isPassword ? t('password_change_success') : t('profile_change_success'),
				'success'
			);
			if (isPassword) {
				passwordModalOpen = false;
			} else {
				editModalOpen = false;
			}
		}
	};

	// Enhance handlers
	const handleEditEnhance = () => {
		return async ({ result, update }) => {
			isSubmitting = true;
			handleSubmissionResult(result);
			await update();
			isSubmitting = false;
		};
	};

	const handlePasswordEnhance = () => {
		return async ({ result, update }) => {
			isPasswordSubmitting = true;
			handleSubmissionResult(result, true);
			await update();
			isPasswordSubmitting = false;
		};
	};

	function openEditModal(user: any) {
		selectInstances = { ...user };
		// Reset form data to current user values
		formData = {
			name: user.name,
			work_email: user.work_email,
			employee_id: user.employee_id,
			first_name: user.first_name,
			last_name: user.last_name,
			department: user.department || '',
			role: user.role,
			is_active: user.is_active
		};
		// Reset original form data for change detection
		originalFormData = {
			name: user.name,
			work_email: user.work_email,
			employee_id: user.employee_id,
			first_name: user.first_name,
			last_name: user.last_name,
			department: user.department || '',
			role: user.role,
			is_active: user.is_active
		};
		editModalOpen = true;
	}

	// Watch modal state to clean up everything
	$: if (passwordModalOpen === false) {
		passwordFormData = {
			new_password: '',
			confirm_password: ''
		};
		passwordFieldsEverTyped = false;
		// Reset password visibility states
		showNewPassword = false;
		showConfirmPassword = false;
		dismissPasswordAlerts();
	}

	// Form data with initial values from user
	let formData = {
		name: user.name,
		username: user.username,
		work_email: user.work_email,
		employee_id: user.employee_id,
		first_name: user.first_name,
		last_name: user.last_name,
		department: user.department || '',
		role: user.role,
		is_active: user.is_active
	};

	// Store original values for change detection
	let originalFormData = {
		name: user.name,
		username: user.username,
		work_email: user.work_email,
		employee_id: user.employee_id,
		first_name: user.first_name,
		last_name: user.last_name,
		department: user.department || '',
		role: user.role,
		is_active: user.is_active
	};

	// Track if form has changes
	$: hasFormChanges =
		formData.name !== originalFormData.name ||
		formData.username !== originalFormData.username || 
		formData.work_email !== originalFormData.work_email ||
		formData.employee_id !== originalFormData.employee_id ||
		formData.first_name !== originalFormData.first_name ||
		formData.last_name !== originalFormData.last_name ||
		formData.department !== originalFormData.department ||
		formData.role !== originalFormData.role ||
		formData.is_active !== originalFormData.is_active;

	// onst roles = ['Admin', 'Supervisor', 'Agent']; // Update based on your available roles
</script>

<Button
	color="none"
	class="w-full justify-start p-0 text-left hover:bg-gray-100"
	on:click={() => openEditModal(user)}
>
	{t('user_edit_user')}
</Button>
<Modal
	bind:open={editModalOpen}
	size="md"
	outsideclose
	class={passwordModalOpen ? 'dark:brightness-80 brightness-50' : ''}
>
	<h2 slot="header">{t('user_edit_user')}</h2>
	<div>
		<form
			bind:this={editForm}
			action="?/update_user"
			method="POST"
			use:enhance={handleEditEnhance}
			class="space-y-3"
		>
			<input type="hidden" name="id" value={user.id} />
			<input type="hidden" name="username" value={user.username} />

			<div class="grid gap-2">
				<Label for="name" class="text-left">{t('username')}</Label>
				<Input
					id="name"
					name="name"
					type="text"
					placeholder="Enter name"
					bind:value={user.username}
					required
					disabled
				/>
			</div>

			<div class="grid grid-cols-2 gap-4">
				<div class="grid gap-2">
					<Label for="first_name" class="space-y-2 text-left">{t('first_name')}</Label>
					<Input
						id="first_name"
						name="first_name"
						type="text"
						placeholder="Enter first name"
						bind:value={formData.first_name}
						required
					/>
				</div>

				<div class="grid gap-2">
					<Label for="last_name" class="space-y-2 text-left">{t('last_name')}</Label>
					<Input
						id="last_name"
						name="last_name"
						type="text"
						placeholder="Enter last name"
						bind:value={formData.last_name}
						required
					/>
				</div>
			</div>

			<div class="grid grid-cols-2 gap-4">
				<div class="grid gap-2">
					<Label for="work_email" class="space-y-2 text-left">{t('signup_form_work_email')}</Label>
					<Input
						id="work_email"
						name="work_email"
						type="email"
						placeholder={t('signup_form_required_badge')}
						bind:value={formData.work_email}
						required
					/>
				</div>
				{#if role === 'Admin'}
					<div class="gap-2-left grid text-left">
						<Label for="password" class="space-y-2 text-left">{t('password')}</Label>
						<div class="mt-2">
							<Button
								type="button"
								class="w-auto whitespace-nowrap rounded-lg border border-gray-300 bg-white font-medium text-black transition-colors hover:bg-blue-500 hover:text-white"
								on:click={(e) => {
									e.stopPropagation();
									passwordModalOpen = true;
								}}
							>
								{t('change_password')}
							</Button>
						</div>
					</div>
				{/if}
			</div>
			<!-- {#if role === 'Admin'}		// Reactive/Deactivate the user from dropdown menu instead
				<Checkbox
					class="flex items-center gap-1"
					bind:checked={formData.is_active}
					bind:value={formData.is_active}
				>
					{t('active')}
				</Checkbox>
			{/if} -->
			<input type="hidden" name="is_active" value={formData.is_active} />
		</form>
		<div class="mt-6 flex items-center gap-2">
			<Button
				type="submit"
				color="blue"
				on:click={() => editForm.requestSubmit()}
				disabled={isSubmitting || !hasFormChanges}
			>
				{#if isSubmitting}
					{t('saving')}
				{:else}
					{t('confirm')}
				{/if}
			</Button>
			<Button color="alternative" on:click={() => (editModalOpen = false)}>{t('cancel')}</Button>
		</div>
	</div>
</Modal>

<Modal bind:open={passwordModalOpen} size="xs" outsideclose>
	<div class="mb-4 text-2xl font-semibold text-black">{t('change_password')}</div>
	<div class="p-2">
		<form
			bind:this={passwordForm}
			action="?/force_change_password"
			method="POST"
			use:enhance={handlePasswordEnhance}
			class="space-y-3"
		>
			<input type="hidden" name="user_id" value={user.id} />
			<div class="grid gap-2">
				<Label for="new_password" class="space-y-2 text-left text-sm">{t('new_password')}</Label>
				<div class="relative">
					<Input
						id="new_password"
						name="new_password"
						type={showNewPassword ? 'text' : 'password'}
						bind:value={passwordFormData.new_password}
						placeholder={t('new_password_placeholder')}
						required
						on:input={handlePasswordInput}
						class="pr-10"
					/>
					<button
						type="button"
						class="absolute inset-y-0 right-0 flex items-center pr-3 text-gray-500 hover:text-gray-700 focus:outline-none"
						on:click={toggleNewPasswordVisibility}
						aria-label={showNewPassword ? 'Hide new password' : 'Show new password'}
						tabindex="-1"
					>
						{#if showNewPassword}
							<EyeSlashSolid class="h-5 w-5" />
						{:else}
							<EyeSolid class="h-5 w-5" />
						{/if}
					</button>
				</div>
				<div>
					<div class="mb-1 mt-2 text-xs font-normal text-gray-400">
						{t('password_validation_msg_1')}
					</div>
					<ul class="space-y-0 text-xs">
						<li class="flex items-center">
							<span
								class={passwordRulesStatus.length
									? 'text-green-600'
									: passwordFieldsEverTyped
										? 'text-red-600'
										: 'text-gray-400'}>{t('password_validation_msg_2')}</span
							>
						</li>
						<li class="flex items-center">
							<span
								class={passwordRulesStatus.lowercase && passwordRulesStatus.uppercase
									? 'text-green-600'
									: passwordFieldsEverTyped
										? 'text-red-600'
										: 'text-gray-400'}>{t('password_validation_msg_3')}</span
							>
						</li>
						<li class="flex items-center">
							<span
								class={passwordRulesStatus.number
									? 'text-green-600'
									: passwordFieldsEverTyped
										? 'text-red-600'
										: 'text-gray-400'}>{t('password_validation_msg_4')}</span
							>
						</li>
						<li class="flex items-center">
							<span
								class={passwordRulesStatus.special
									? 'text-green-600'
									: passwordFieldsEverTyped
										? 'text-red-600'
										: 'text-gray-400'}>{t('password_validation_msg_5')} ({specialChars})</span
							>
						</li>
					</ul>
				</div>
			</div>
			<div class="grid gap-2">
				<Label for="confirm_password" class="space-y-2 text-left text-sm"
					>{t('confirm_password')}</Label
				>
				<div class="relative">
					<Input
						id="confirm_password"
						name="confirm_password"
						type={showConfirmPassword ? 'text' : 'password'}
						bind:value={passwordFormData.confirm_password}
						placeholder={t('confirm_new_password_placeholder')}
						required
						on:input={handlePasswordInput}
						class="pr-10"
					/>
					<button
						type="button"
						class="absolute inset-y-0 right-0 flex items-center pr-3 text-gray-500 hover:text-gray-700 focus:outline-none"
						on:click={toggleConfirmPasswordVisibility}
						aria-label={showConfirmPassword ? 'Hide confirm password' : 'Show confirm password'}
						tabindex="-1"
					>
						{#if showConfirmPassword}
							<EyeSlashSolid class="h-5 w-5" />
						{:else}
							<EyeSolid class="h-5 w-5" />
						{/if}
					</button>
				</div>
				<div style="min-height:1em;" class="justify-left items-top flex">
					{#if passwordFieldsEverTyped && !passwordsMatch && passwordFormData.confirm_password.length > 0}
						<span class="text-left text-xs text-red-600"
							>{t('password_validation_msg_do_not_match')}</span
						>
					{/if}
				</div>
			</div>
			<div class="mt-6 flex items-center gap-2">
				<Button
					type="submit"
					color="blue"
					disabled={!passwordsMatch || !allPasswordRulesPassed || isPasswordSubmitting}
				>
					{#if isPasswordSubmitting}
						{t('saving')}
					{:else}
						{t('confirm')}
					{/if}
				</Button>
				<Button
					color="alternative"
					on:click={() => {
						passwordModalOpen = false;
					}}
				>
					{t('cancel')}
				</Button>
			</div>
		</form>
	</div>
</Modal>
