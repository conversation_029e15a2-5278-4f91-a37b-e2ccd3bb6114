<script lang="ts">
    import {
        Textarea,
        Button,
        Hr,
        Dropdown,
        DropdownItem
	} from 'flowbite-svelte';
    import {
        TrashBinOutline,
        PenOutline,
        PlusOutline,
        AngleDownOutline
    } from 'flowbite-svelte-icons';
    import { t, language } from '$lib/stores/i18n';

    import { enhance } from '$app/forms';

    import NoteEditModal from '$src/routes/(site)/monitoring/[id]/InfoDisplayPanel/NoteEditModal.svelte';
    import NoteDeleteModal from '$src/routes/(site)/monitoring/[id]/InfoDisplayPanel/NoteDeleteModal.svelte';
    import { getColorClass, formatTimestamp } from '$lib/utils';

    let formEl: HTMLFormElement;          // ref to the form

    /** Submit on Enter (unless Shift is held) */
    function handleKeydown(e: KeyboardEvent) {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();            // stop newline
            formEl?.requestSubmit();       // native submit → still goes through use:enhance
        }
    }
    export let customer: any;          // customer object
    export let customer_notes: any[];    // customer notes array

    // Note management variables
    let editModal = false;
    let deleteModal = false;
    let editNote;
    let deleteNoteId;

    // Functions for note operations
    function openEditModal(note) {
        editModal = true;
        editNote = { ...note };
    }

    function openDeleteModal(id) {
        deleteModal = true;
        deleteNoteId = id;
    }

    // Function to format date
    const displayDateDraft = (timestamp) => {
        const displayCreated = new Date(timestamp);
        
        // Format each part separately
        const day = displayCreated.getDate().toString().padStart(2, '0');
        const month = displayCreated.toLocaleString('en-US', { month: 'short' });
        const year = displayCreated.getFullYear();
        
        // Add hour and minute in 24-hour format
        const hour = displayCreated.getHours().toString().padStart(2, '0');
        const minute = displayCreated.getMinutes().toString().padStart(2, '0');
        
        // Combine in desired format
        return `${day} ${month} ${year} ${hour}:${minute}`;
    };
</script>

<div>
    <!-- Notes Form -->
    <form
        bind:this={formEl}
        method="POST"
        enctype="multipart/form-data"
        action="?/upload_note"
        use:enhance={() => {
            return async ({ update, result }) => {
                if (result.type === 'success') {
                    await update();            // refresh data
                }
            };
        }}
    >
        <!-- note + send -->
        <input type="hidden" name="customerId" value={customer.customer_id} />

        <div class="mb-3">
            <Textarea
                rows="2"
                name="note"
                placeholder="Type note…"
                required
                on:keydown={handleKeydown}     
                class="mb-4 p-4 bg-white rounded-lg border shadow-sm"
            />

            <!-- still keeps mouse-friendly button -->
            <Button
                type="submit"
                color="light"
            >
                <PlusOutline class="w-4 h-4 mr-2" />
                {t('add_note')}
            </Button>

        </div>
    </form>

    <Hr classHr="my-3" />

    <!-- Customer Notes -->
    <div class="space-y-4">
        <!-- <ul> -->
            {#each customer_notes as note (note.id)}
                <!-- <li class="mb-4 border-b pb-4"> -->
                    <div class="bg-white rounded-lg shadow-sm border border-gray-100 p-3">
                        <div class="text-md text-gray-700 whitespace-pre-wrap leading-relaxed">
                            {note.content}
                        </div>
                        <div class="grid grid-cols-1 gap-4 md:grid-cols-[8fr_1fr]">
                            <div class="flex space-x-4">
                                <p class="inline text-sm text-gray-500">
                                    {note.updated_on
                                        ? formatTimestamp(note.updated_on)
                                        : formatTimestamp(note.created_on)}
                                </p>
                                <p class="inline text-sm text-gray-900">
                                    {note.updated_by_name ? note.updated_by_name : note.created_by_name || 'User'}
                                </p>
                            </div>
                            <div class="flex space-x-2">
                                <Button
                                    color="light"
                                    class="flex h-6 w-6 items-center justify-center rounded-full p-2 text-gray-500"
                                >
                                    <AngleDownOutline class="h-4 w-4" />
                                </Button>

                                <Dropdown>
                                    <DropdownItem class="flex items-center space-x-2" on:click={openEditModal(note)}>
                                        {t('edit')} <PenOutline class="h-4 w-4" />
                                    </DropdownItem>
                                    <DropdownItem
                                        class="flex items-center space-x-2"
                                        on:click={openDeleteModal(note.id)}
                                    >
                                        {t('delete')} <TrashBinOutline class="h-4 w-4" />
                                    </DropdownItem>
                                </Dropdown>
                            </div>
                            
                        </div>
                    </div>
                <!-- </li> -->
            {/each}
        <!-- </ul> -->
    </div>

    <!-- Note Edit Modal -->
    <NoteEditModal 
        bind:editModal 
        editNote={editNote} 
        customerId={customer.customer_id} 
    />

    <!-- Note Delete Modal -->
    <NoteDeleteModal 
        bind:deleteModal 
        deleteNoteId={deleteNoteId} 
        customerId={customer.customer_id} 
    />
</div>
