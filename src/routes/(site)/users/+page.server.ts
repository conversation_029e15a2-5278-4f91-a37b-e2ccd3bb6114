// +page.server.ts
import type { PageServerLoad } from "./$types";
import { services } from "$lib/api/features";
import { error, fail, redirect } from "@sveltejs/kit";
import type { Actions } from "./$types";

export const load: PageServerLoad = async ({ cookies }) => {
    let access_token = cookies.get('access_token');
    let refresh_token = cookies.get('refresh_token');
    if (!access_token) {
        return {
            users: [],
            statuses: [],
            roles: [],
            partners: [],
            all_tags: [],
            // workshift: [],
            error: 'No access token available'
        };
    }

    for (let repeatCount = 0; repeatCount < 3; repeatCount++) {
        try {
            // Remove users.getAll() - let client handle all user fetching with pagination
            const status_response = await services.statuses.getAll(access_token);
            const role_response = await services.roles.getAll(access_token);
            
            // Get filter data from new endpoints
            const filter_partners_response = await services.users.getFilterPartners(access_token);
            const filter_departments_response = await services.users.getFilterDepartments(access_token);
            const filter_tags_response = await services.users.getFilterSpecializedTags(access_token);
            // const filter_workshift_response = await services.users.getFilterSchedules(access_token);

            if (status_response.res_status === 401 || role_response.res_status === 401 || 
                filter_partners_response.res_status === 401 || filter_departments_response.res_status === 401 || 
                filter_tags_response.res_status === 401 ) {
                throw error(401, 'Invalid access token!!!');
            }

            return {
                users: [], // Start with empty array - client will fetch paginated users
                statuses: status_response.statuses || [],
                roles: role_response.roles || [],
                partners: filter_partners_response.partners || [],
                departments: filter_departments_response.departments || [],
                all_tags: filter_tags_response.specialized_tags || [],
                // workshift: filter_workshift_response.schedules || [],
                token: access_token
            };
        } catch (err) {
            // console.error('Error fetching user details:', err);
            // error(500, 'Failed to load user details');

            const refreshResponse = await services.users.refreshToken(refresh_token);
            const login_token = refreshResponse.login_token;

            if (login_token.length === 0) {
                cookies.set("isLogin", 'false', { path: '/' })
                throw redirect(302, '/login');
            } else {
                access_token = login_token.access;
                refresh_token = login_token.refresh;

                cookies.set("access_token", access_token, { path: '/' });
                cookies.set("refresh_token", refresh_token, { path: '/' })
            }
        }
    }
}


export const actions: Actions = {
    sign_up_user: async ({ request, fetch, cookies }) => {
        const access_token = cookies.get('access_token');
        const formData = await request.formData();

        const newUserData = {
			// Basic Info (Required)
            // employee_id: formData.get('employee_id'),
            username: formData.get('username'),
            password: formData.get('password'),
            confirm_password: formData.get('confirm_password'),
            name: formData.get('name'),
            first_name: formData.get('first_name'),
            last_name: formData.get('last_name'),
            // department: formData.get('department'),
            // role: formData.get('role'),
            is_active: formData.get('is_active'),
            is_staff: formData.get('is_staff'),
            is_superuser: formData.get('is_superuser'),

            // Contact Info (Optional)
			personal_phone: formData.get('personal_phone'),
			work_phone: formData.get('work_phone'),
			personal_email: formData.get('personal_email'),
			work_email: formData.get('work_email'),

			// Preferences (Optional)
			preferred_language: formData.get('preferred_language'),

			// Emergency Contact (Optional)
			emergency_contact_name: formData.get('emergency_contact_name'),
			emergency_contact_phone: formData.get('emergency_contact_phone'),
			emergency_contact_email: formData.get('emergency_contact_email'),
        };

        // TODO - Delete this
        // console.log(formData)
        // console.log(`newUserData - ${JSON.stringify(newUserData)}`)

        const response = await services.users.signUpNewUser(newUserData, access_token)
        if (response.error_msg) {
            return fail(response.res_status, { error: `${response.error_msg}` });
        }
        let res_msg = response.res_msg;

        return { success: true, res_msg };
    },
};