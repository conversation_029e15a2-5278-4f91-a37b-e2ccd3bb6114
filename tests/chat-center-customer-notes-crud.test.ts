/**
 * End-to-End Test: Chat Center Customer Notes CRUD Operations Workflow
 *
 * This comprehensive test validates the complete customer notes CRUD (Create, Read, Update, Delete)
 * workflow in the InformationTab component, ensuring proper functionality of note management
 * features including creation, search, editing, and deletion with round-trip testing patterns.
 *
 * SVELTEKIT PAGES TESTED:
 * - /chat_center (+page.svelte) - Main chat center interface with customer information panel
 *   └── Loads platform identities and customer data via +page.server.ts load function
 *   └── Integrates CustomerInfoPanel for customer details display and note management
 *
 * SVELTE COMPONENTS TESTED:
 * - PlatformIdentityList.svelte - Chat conversation list with tab navigation
 *   └── Tab buttons: platform-list-chat-tab-{tab.id} (line 736)
 *   └── Chat items: [data-testid="chat-item"] (line 770)
 *   └── Individual chat IDs: #platform-list-chat-item-{identity.id} (line 764)
 * - CustomerInfoPanel.svelte - Customer information display with tab navigation
 *   └── Information tab button for accessing customer notes functionality
 * - InformationTab.svelte - Main component for customer notes CRUD operations
 *   └── Notes section: #info-tab-notes-section (line 603)
 *   └── Notes toggle: #info-tab-notes-toggle (line 606)
 *   └── Add note form: #info-tab-add-note-form (line 644)
 *   └── Note content textarea: #info-tab-note_content (line 666)
 *   └── Add note button: #info-tab-add-note-button (line 676)
 *   └── Search input: #info-tab-search-note (line 684)
 *   └── Notes list: #info-tab-notes-list (line 699)
 *   └── Individual notes: #info-tab-note-{note.id} (line 707)
 *   └── No notes found: #info-tab-no-notes-found (line 701)
 * - NoteEditModal.svelte - Modal for editing existing customer notes
 *   └── Edit textarea: #note-edit-modal-textarea (line 39)
 *   └── Save button: #note-edit-modal-save-button (green color)
 *   └── Cancel button: #note-edit-modal-cancel-button (light color)
 * - NoteDeleteModal.svelte - Modal for confirming note deletion
 *   └── Delete button: #note-delete-modal-delete-button (red color)
 *   └── Cancel button: #note-delete-modal-cancel-button (light color)
 *   └── Confirmation dialog with exclamation icon
 *
 * COMPLETE WORKFLOW TESTED:
 * 1. Authentication and navigation to chat center page (/chat_center)
 * 2. Tab selection and conversation selection from platform identity list
 * 3. Navigation to Information tab in CustomerInfoPanel
 * 4. Create Note Test: Generate random content, create new note, verify creation
 * 5. Search Note Test: Search for created note, verify search results, clear search
 * 6. Edit Note Test: Edit existing note content, verify changes are saved
 * 7. Delete Note Test: Delete note, confirm deletion, verify removal
 *
 * ROUND-TRIP TESTING PATTERN:
 * - Captures original state before operations
 * - Performs CRUD operations with verification
 * - Restores original state to prevent test environment pollution
 * - Uses language-agnostic DOM attribute assertions
 *
 * ID SELECTOR STRATEGY:
 * All ID selectors use the "info-tab-" prefix pattern established in InformationTab.svelte.
 * Each selector references actual HTML elements with documented line numbers for maintainability.
 * Data-testid attributes are used as fallbacks for more stable test selectors.
 */

import { test, expect } from '@playwright/test';
import type { Page } from '@playwright/test';

test.use({ viewport: { width: 1920, height: 1080 } });

// Import authentication utilities
import { performLoginWithRedirectHandling, waitForFinalDestination } from './utils/auth.utils';

/**
 * Utility function to wait for PlatformIdentityList component to load
 * Ensures the chat list is ready for interaction
 */
async function waitForPlatformIdentityListLoad(page: Page) {
	console.log('Waiting for PlatformIdentityList to load...');
	await page.waitForSelector('#platform-list-chat-center-title', { timeout: 15000 });
	const chatItems = page.locator('#platform-list-chat-center-title');
	await expect(chatItems.first()).toBeVisible();
	console.log('✓ PlatformIdentityList loaded successfully');
}

/**
 * Utility function to select any available chat from the platform identity list
 * Tries different tabs to find available chats
 */
async function selectAvailableChat(page: Page) {
	console.log('Selecting available chat...');
	
	// Try different tabs to find available chats
	const tabSelectors = [
		'#platform-list-chat-tab-my-assigned',
		'#platform-list-chat-tab-my-closed',
		'#platform-list-chat-tab-open',
		'#platform-list-chat-tab-other-assigned',
	];
	
	for (const tabSelector of tabSelectors) {
		try {
			const tab = page.locator(tabSelector);
			if (await tab.isVisible()) {
				await tab.click();
				await page.waitForTimeout(1000);
				
				const chatItems = page.locator('[data-testid="chat-item"]');
				const itemCount = await chatItems.count();
				
				if (itemCount > 0) {
					console.log(`Found ${itemCount} chats in tab, selecting first one`);
					await chatItems.first().click();
					await page.waitForTimeout(2000);
					return true;
				}
			}
		} catch (error) {
			console.log(`Tab ${tabSelector} not available or has no items`);
		}
	}
	
	throw new Error('No available chats found in any tab');
}

/**
 * Utility function to navigate to Information tab in CustomerInfoPanel
 */
async function navigateToInformationTab(page: Page) {
	console.log('Navigating to Information tab...');
	
	// Wait for customer info panel to load
	await page.waitForSelector('[data-testid="information-tab"]', { timeout: 10000 });
	
	// Check if Information tab button exists and click it
	const infoTabButton = page.locator('#customer-info-customer-tab-information');
	if (await infoTabButton.isVisible()) {
		await infoTabButton.click();
		await page.waitForTimeout(1000);
	}
	
	// Verify we're on the Information tab
	await expect(page.locator('[data-testid="information-tab"]')).toBeVisible();
	console.log('✓ Successfully navigated to Information tab');
}

/**
 * Utility function to ensure notes section is expanded
 */
async function ensureNotesSectionExpanded(page: Page) {
	console.log('Ensuring notes section is expanded...');
	
	const notesToggle = page.locator('#info-tab-notes-toggle');
	await expect(notesToggle).toBeVisible();
	
	// Check if notes content is visible, if not click toggle
	const notesContent = page.locator('#info-tab-notes-content');
	if (!(await notesContent.isVisible())) {
		await notesToggle.click();
		await page.waitForTimeout(500);
	}
	
	await expect(notesContent).toBeVisible();
	console.log('✓ Notes section is expanded');
}

/**
 * Generate random text content for testing
 */
function generateRandomNoteContent(): string {
	const timestamp = Date.now();
	const randomSuffix = Math.random().toString(36).substring(7);
	return `Test note content ${timestamp} - ${randomSuffix}`;
}

test.describe('Chat Center Customer Notes CRUD Operations', () => {
	test('should perform complete CRUD operations on customer notes', async ({ page }) => {
		// Step 1: Authentication and navigation to chat center
		console.log('\n--- Step 1: Authentication and Navigation ---');
		await performLoginWithRedirectHandling(page);
		await expect(page).toHaveURL('/chat_center');
		console.log('✓ Successfully navigated to chat center');
		
		// Step 2: Wait for PlatformIdentityList component to load
		console.log('\n--- Step 2: Loading Platform Identity List ---');
		await waitForPlatformIdentityListLoad(page);
		
		// Step 3: Select any available chat
		console.log('\n--- Step 3: Selecting Available Chat ---');
		await selectAvailableChat(page);
		
		// Step 4: Navigate to Information tab
		console.log('\n--- Step 4: Navigating to Information Tab ---');
		await navigateToInformationTab(page);
		
		// Step 5: Ensure notes section is expanded
		console.log('\n--- Step 5: Expanding Notes Section ---');
		await ensureNotesSectionExpanded(page);
		
		// Capture original notes count for round-trip testing
		const originalNotesCount = await page.locator('#info-tab-notes-list [data-testid="note-item"]').count();
		console.log(`Original notes count: ${originalNotesCount}`);
		
		// Step 6: Create Note Test
		console.log('\n--- Step 6: Create Note Test ---');
		const testNoteContent = generateRandomNoteContent();
		console.log(`Creating note with content: "${testNoteContent}"`);
		
		// Fill note content textarea
		const noteTextarea = page.locator('#info-tab-note_content');
		await expect(noteTextarea).toBeVisible();
		await noteTextarea.fill(testNoteContent);
		
		// Verify add button is enabled
		const addButton = page.locator('#info-tab-add-note-button');
		await expect(addButton).toBeVisible();
		await expect(addButton).toBeEnabled();
		
		// Submit the note
		await addButton.click();
		await page.waitForTimeout(3000); // Wait for note creation
		
		// Verify note was created
		const newNotesCount = await page.locator('#info-tab-notes-list [data-testid="note-item"]').count();
		expect(newNotesCount).toBe(originalNotesCount + 1);
		
		// Verify the note content appears in the list
		const noteItems = page.locator('#info-tab-notes-list [data-testid="note-item"]');
		const noteWithContent = noteItems.filter({ hasText: testNoteContent });
		await expect(noteWithContent).toBeVisible();
		console.log('✓ Note created successfully');
		
		// Step 7: Search Note Test
		console.log('\n--- Step 7: Search Note Test ---');
		const searchInput = page.locator('#info-tab-search-note');
		await expect(searchInput).toBeVisible();
		
		// Search for a unique part of our test note
		const searchTerm = testNoteContent; // Use timestamp part
		await searchInput.fill(searchTerm);
		await page.waitForTimeout(1000); // Wait for search filtering
		
		// Verify search results
		const filteredNotes = page.locator('#info-tab-notes-list [data-testid="note-item"]');
		const filteredCount = await filteredNotes.count();
		expect(filteredCount).toBeGreaterThanOrEqual(1);
		
		// Verify our note is in the search results
		await expect(noteWithContent).toBeVisible();
		console.log('✓ Search functionality working correctly');
		
		// Clear search
		await searchInput.clear();
		await page.waitForTimeout(1000);
		
		// Verify all notes are visible again
		const allNotesAfterClear = await page.locator('#info-tab-notes-list [data-testid="note-item"]').count();
		expect(allNotesAfterClear).toBe(newNotesCount);
		console.log('✓ Search cleared successfully');
		
		// Step 8: Edit Note Test
		console.log('\n--- Step 8: Edit Note Test ---');
		const updatedNoteContent = generateRandomNoteContent();
		console.log(`Updating note content to: "${updatedNoteContent}"`);
		
		// Find our test note and open edit modal
		const testNote = noteItems.filter({ hasText: testNoteContent }).first();
		await expect(testNote).toBeVisible();
		
		// Click the dropdown button (AngleDownOutline button)
		const dropdownButton = testNote.locator('button').filter({ hasText: '' }).first();
		await dropdownButton.click();
		await page.waitForTimeout(500);
		
		// Click edit option
		const editOption = page.locator('#info-tab-edit-note').first();
		await editOption.click();
		await page.waitForTimeout(1000);
		
		// Verify edit modal is open and update content
		const editTextarea = page.locator('#note-edit-modal-textarea');
		await expect(editTextarea).toBeVisible();
		await editTextarea.clear();
		await editTextarea.fill(updatedNoteContent);
		
		// Click save button
		const saveButton = page.locator('#note-edit-modal-save-button');
		await saveButton.click();
		await page.waitForTimeout(3000); // Wait for update
		
		// Verify note content was updated
		const updatedNote = noteItems.filter({ hasText: updatedNoteContent });
		await expect(updatedNote).toBeVisible();
		console.log('✓ Note edited successfully');
		
		// Step 9: Delete Note Test
		console.log('\n--- Step 9: Delete Note Test ---');
		
		// Find the updated note and open delete modal
		await expect(updatedNote).toBeVisible();
		
		// Click the dropdown button
		const deleteDropdownButton = updatedNote.locator('button').filter({ hasText: '' }).first();
		await deleteDropdownButton.click();
		await page.waitForTimeout(500);
		
		// Click delete option
		const deleteOption = page.locator('#info-tab-delete-note').first();
		await deleteOption.click();
		await page.waitForTimeout(1000);
		
		// Verify delete confirmation modal is open
		const deleteModal = page.locator('#note-delete-modal-container');
		await expect(deleteModal).toBeVisible();
		
		// Click delete confirmation button
		const confirmDeleteButton = page.locator('#note-delete-modal-delete-button');
		await confirmDeleteButton.click();
		await page.waitForTimeout(3000); // Wait for deletion
		
		// Verify note was deleted
		const finalNotesCount = await page.locator('#info-tab-notes-list [data-testid="note-item"]').count();
		expect(finalNotesCount).toBe(originalNotesCount);
		
		// Verify the note is no longer visible
		await expect(updatedNote).not.toBeVisible();
		console.log('✓ Note deleted successfully');
		
		// Step 10: Verify search doesn't find deleted note
		console.log('\n--- Step 10: Verifying Deleted Note Not Found in Search ---');
		await searchInput.fill(updatedNoteContent);
		await page.waitForTimeout(1000);
		
		// Should show no notes found or empty results
		const searchResults = await page.locator('#info-tab-notes-list [data-testid="note-item"]').count();
		expect(searchResults).toBe(0);
		
		// Clear search for cleanup
		await searchInput.clear();
		await page.waitForTimeout(1000);
		
		console.log('✓ CRUD operations completed successfully');
		console.log('✓ Round-trip testing completed - original state restored');
	});
});
