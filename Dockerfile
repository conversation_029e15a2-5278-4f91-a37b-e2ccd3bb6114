FROM node:22-bookworm

ARG BUILD_VERSION=local
ENV BUILD_VERSION=${BUILD_VERSION}

ARG WORKDIR=/src/app
ARG VENDORDIR=/src/vendor
WORKDIR ${WORKDIR}


# CREATE USER
ARG USERNAME=node
ARG USER_UID=1000
ARG USER_GID=$USER_UID

# # Create the user
# RUN groupadd --gid $USER_GID $USERNAME \
#     && useradd --uid $USER_UID --gid $USER_GID -m $USERNAME \
    #
    # [Optional] Add sudo support. Omit if you don't need to install software after connecting.
RUN apt-get update \
    && apt-get install -y sudo \
    && echo $USERNAME ALL=\(root\) NOPASSWD:ALL > /etc/sudoers.d/$USERNAME \
    && chmod 0440 /etc/sudoers.d/$USERNAME

RUN apt update
RUN apt install -y vim

COPY --chown=${USER_UID}:${USER_GID} . ${WORKDIR}
RUN chown -R ${USER_UID}:${USER_GID} ${WORKDIR}

# Clear apt for optimizing image size
RUN apt clean
RUN rm -rf /var/lib/apt/lists/* /tmp/* /var/tmp/*

# Inject dummy .env to make build pass
ENV PUBLIC_BACKEND_URL=http://dummy
ENV PUBLIC_LIFF_ID=http://dummy

# [Optional] Set the default user. Omit if you want to keep the default as root.
USER $USERNAME
RUN npm install
RUN npm run build
CMD npm run dev